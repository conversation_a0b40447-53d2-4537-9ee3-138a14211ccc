# XXL-Job 配置指南 - 第三方下架商品同步任务

## 任务概述

**任务名称**: `syncThirdPartyOffSaleProducts`  
**任务类**: `ThirdPartyOffSaleProductSyncJob`  
**功能**: 同步第三方系统的商品下架状态，将本地上架但第三方已下架的商品更新为下架状态  
**数据规模**: 针对100万+商品数据进行优化  

## XXL-Job 管理端配置步骤

### 1. 登录 XXL-Job 管理界面
访问 XXL-Job 管理界面并登录

### 2. 创建新任务
点击"任务管理" -> "新增"

### 3. 基本信息配置
- **任务描述**: 第三方下架商品同步任务
- **负责人**: 填写负责人信息
- **报警邮件**: 填写报警邮件地址（可选）

### 4. 调度配置
- **调度类型**: CRON
- **Cron表达式**: 
  - 每2小时执行: `0 0 */2 * * ?`
  - 每4小时执行: `0 0 */4 * * ?`
  - 每天凌晨2点执行: `0 0 2 * * ?`
  - 建议选择: `0 0 */4 * * ?` (每4小时执行一次)

### 5. 任务配置
- **运行模式**: BEAN
- **JobHandler**: `syncThirdPartyOffSaleProducts`
- **执行参数**: 留空（使用默认参数）
- **路由策略**: 第一个
- **子任务ID**: 留空
- **任务超时时间**: 7200 (2小时，考虑大数据量处理时间)
- **失败重试次数**: 1

### 6. 高级配置
- **负责人**: 填写任务负责人
- **报警邮件**: 配置异常报警邮件
- **任务描述**: 同步第三方系统下架商品状态，处理100万+商品数据

## 任务执行参数说明

### 默认参数配置
```java
// 分页参数
pageSize = 1000        // 每页1000条，减少请求次数
maxPages = 2000        // 最大处理2000页，防止无限循环

// 批处理参数  
batchSize = 500        // 每批500条下架操作，避免单次操作过大

// 延迟参数
requestDelay = 50ms    // 请求间延迟50ms，保护第三方系统
batchDelay = 50ms      // 批次间延迟50ms，避免数据库压力
```

### 性能预估
- **数据量**: 100万条记录
- **分页数**: 约1000页 (1000条/页)
- **处理时间**: 预计30-60分钟
- **内存使用**: 适中（分页+分批处理）

## 监控和日志

### 执行日志位置
- XXL-Job 管理界面 -> 调度日志
- 应用日志文件: `goods-services/logs/`

### 关键监控指标
1. **数据获取阶段**
   - 第三方系统返回的总记录数
   - 分页获取进度
   - 有效oneId数量

2. **数据对比阶段**
   - 本地上架商品数量
   - 需要下架的商品数量

3. **批量下架阶段**
   - 分批处理进度
   - 每批更新数量
   - 总更新数量

4. **执行结果**
   - 总执行时间
   - 成功/失败状态
   - 异常信息（如有）

### 日志示例
```
开始执行第三方下架商品同步任务，参数配置: pageSize=1000, maxPages=2000, batchSize=500
第三方系统总共有 1000000 条已下架记录，分为 1000 页
从第三方系统获取到 1000000 个已下架的oneId
本地上架状态商品共 800000 个
发现 50000 个商品需要下架
开始分批下架操作，总数量: 50000，批大小: 500，总批次: 100
第三方下架商品同步任务执行完成，耗时: 1800000 ms，成功下架 50000 个商品
```

## 异常处理

### 常见异常情况
1. **第三方接口不可用**: 任务会记录错误并终止
2. **网络超时**: 单页失败会终止任务，避免数据不一致
3. **数据库连接异常**: 单批失败不影响其他批次
4. **内存不足**: 通过分页和分批处理避免

### 异常恢复
- 任务支持重新执行
- 幂等性设计，重复执行不会产生副作用
- 失败重试机制，最多重试1次

## 注意事项

1. **执行频率**: 不建议过于频繁，每4小时执行一次较为合适
2. **系统资源**: 大数据量处理时会占用一定的CPU和内存资源
3. **第三方接口**: 确保第三方接口 `http://localhost:7570/api/system/get_not_on_sale_products` 可用
4. **数据一致性**: 任务执行期间避免手动修改商品状态
5. **监控报警**: 建议配置执行失败的邮件报警

## 测试建议

### 首次部署测试
1. 先在测试环境验证任务执行
2. 检查日志输出是否正常
3. 验证数据更新是否正确
4. 确认性能表现是否符合预期

### 生产环境部署
1. 选择业务低峰期首次执行
2. 密切监控首次执行的日志和性能
3. 根据实际执行情况调整调度频率
4. 建立监控报警机制
