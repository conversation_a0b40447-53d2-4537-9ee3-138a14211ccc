# 第三方下架商品同步任务

## 概述

新增了一个定时任务 `syncOffSaleProductsFromThird`，用于同步第三方系统的商品下架状态。该任务通过调用第三方接口获取已下架商品列表，并将本地对应的上架商品状态更新为下架。

## 功能特性

### 1. 任务配置
- **任务名称**: `syncOffSaleProductsFromThird`
- **默认分页大小**: 20条/页（与第三方接口保持一致）
- **最大处理页数**: 10000页（防止无限循环）
- **请求间隔**: 100ms（避免对第三方系统造成压力）

### 2. 核心逻辑
1. **分页获取第三方已下架商品**: 调用 `http://localhost:7570/api/system/get_not_on_sale_products` 接口
2. **查询本地上架商品**: 获取本地所有 `STATUS = 'ON_SALE'` 的商品
3. **计算需要下架的商品**: 找出本地上架但第三方已下架的商品
4. **批量下架操作**: 使用 `updateExistingProductsToOffSale` 方法批量更新商品状态

### 3. 性能优化
- **分页处理**: 避免一次性加载大量数据
- **集合运算**: 使用 `Set.retainAll()` 高效计算交集
- **批量更新**: 一次性更新所有需要下架的商品
- **请求限流**: 页面间添加延迟，保护第三方系统

## 接口定义

### 第三方接口
```
POST http://localhost:7570/api/system/get_not_on_sale_products
```

**请求参数**:
```json
{
    "pageNum": 1,
    "pageSize": 20
}
```

**响应格式**:
```json
{
    "code": 200,
    "msg": "",
    "data": {
        "records": [
            {
                "oneId": "OCCT2NEY7SG9Y3"
            }
        ],
        "total": 130847,
        "size": 20,
        "current": 1,
        "pages": 6543
    }
}
```

## 新增文件

### 1. 请求DTO
- `KnetGroupGetNotOnSaleProductsReq.java`: 第三方接口请求参数

### 2. 响应DTO
- `KnetNotOnSaleProductVo.java`: 第三方接口响应数据

### 3. 服务接口扩展
- `ApiKnetGroupService`: 新增 `getNotOnSaleProducts` 方法
- `IThirdApiService`: 新增 `getNotOnSaleProducts` 方法
- `ThirdApiServiceImpl`: 实现第三方接口调用逻辑

### 4. 定时任务
- `KnetProductListingCalibrationJob`: 新增 `syncOffSaleProductsFromThird` 方法

## 使用方式

### XXL-Job 配置
1. 在 XXL-Job 管理界面创建新任务
2. 任务名称: `syncOffSaleProductsFromThird`
3. 执行器: 选择对应的执行器
4. 调度配置: 根据业务需求设置执行频率（建议每小时执行一次）

### 日志监控
任务执行过程中会输出详细日志，包括：
- 分页获取进度
- 数据统计信息
- 下架操作结果
- 异常错误信息

## 注意事项

1. **数据一致性**: 任务基于 oneId 进行匹配，确保数据一致性
2. **性能考虑**: 大量数据处理时注意内存使用和执行时间
3. **异常处理**: 网络异常或第三方服务不可用时会记录错误并终止任务
4. **幂等性**: 任务支持重复执行，不会产生副作用

## 监控指标

- **处理商品总数**: 第三方系统返回的已下架商品数量
- **本地上架商品数**: 当前本地上架状态的商品数量
- **实际下架数量**: 成功更新为下架状态的商品数量
- **执行耗时**: 任务总执行时间
- **成功率**: 任务执行成功/失败状态
