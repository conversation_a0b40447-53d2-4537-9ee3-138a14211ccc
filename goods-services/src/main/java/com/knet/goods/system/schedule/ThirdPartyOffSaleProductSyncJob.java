package com.knet.goods.system.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.exception.ServiceException;
import com.knet.goods.model.dto.third.req.KnetGroupGetNotOnSaleProductsReq;
import com.knet.goods.model.dto.third.resp.KnetNotOnSaleProductVo;
import com.knet.goods.service.IKnetProductOperationService;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.IThirdApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/9/8 10:30
 * @description: 第三方系统下架商品同步任务
 */
@Slf4j
@Component
public class ThirdPartyOffSaleProductSyncJob {

    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private IKnetProductService knetProductService;
    @Resource
    private IKnetProductOperationService knetProductOperationService;

    /**
     * 同步第三方系统下架商品状态
     * 通过调用第三方接口获取已下架商品列表，将本地对应商品状态更新为下架
     * 处理大数据量（100万+）商品的下架同步
     */
    @XxlJob("syncThirdPartyOffSaleProducts")
    public ReturnT<String> syncThirdPartyOffSaleProducts() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("同步第三方系统下架商品状态");
        try {
            JobParams params = new JobParams();
            log.info("开始执行第三方下架商品同步任务，参数配置: pageSize={}, maxPages={}, batchSize={}",
                    params.pageSize, params.maxPages, params.batchSize);
            XxlJobHelper.log("开始执行第三方下架商品同步任务，参数配置: pageSize={}, maxPages={}, batchSize={}",
                    params.pageSize, params.maxPages, params.batchSize);
            // 1. 分页获取第三方系统所有已下架的oneId
            Set<String> thirdOffSaleOneIds = getAllOffSaleOneIdsFromThird(params);
            if (CollUtil.isEmpty(thirdOffSaleOneIds)) {
                String message = "从第三方系统未获取到任何已下架的oneId数据";
                log.warn(message);
                XxlJobHelper.log(message);
                return ReturnT.SUCCESS;
            }
            log.info("从第三方系统获取到 {} 个已下架的oneId", thirdOffSaleOneIds.size());
            XxlJobHelper.log("从第三方系统获取到 {} 个已下架的oneId", thirdOffSaleOneIds.size());
            // 2. 查询本地所有上架状态的商品oneId
            Set<String> localOnSaleOneIds = knetProductService.getLocalOnSaleOneIds();
            log.info("本地上架状态商品共 {} 个", localOnSaleOneIds.size());
            XxlJobHelper.log("本地上架状态商品共 {} 个", localOnSaleOneIds.size());
            // 3. 找出需要下架的oneId（本地上架但第三方已下架的）
            Set<String> oneIdsToOffSale = new HashSet<>(localOnSaleOneIds);
            oneIdsToOffSale.retainAll(thirdOffSaleOneIds);
            if (CollUtil.isEmpty(oneIdsToOffSale)) {
                String message = "没有需要下架的商品";
                log.info(message);
                XxlJobHelper.log(message);
                return ReturnT.SUCCESS;
            }
            log.info("发现 {} 个商品需要下架", oneIdsToOffSale.size());
            XxlJobHelper.log("发现 {} 个商品需要下架", oneIdsToOffSale.size());
            // 4. 分批执行下架操作，避免单次操作数据量过大
            int totalUpdated = processBatchOffSale(oneIdsToOffSale, params.batchSize);
            stopWatch.stop();
            String successMessage = String.format("第三方下架商品同步任务执行完成，耗时: %d ms，成功下架 %d 个商品",
                    stopWatch.getTotalTimeMillis(), totalUpdated);
            log.info(successMessage);
            XxlJobHelper.log(successMessage);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format("第三方下架商品同步任务执行失败，耗时: %d ms，错误信息: %s",
                    stopWatch.getTotalTimeMillis(), e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMessage);
        }
    }

    /**
     * 分页获取第三方系统所有已下架的oneId
     *
     * @param params 任务参数
     * @return 所有已下架的oneId集合
     */
    private Set<String> getAllOffSaleOneIdsFromThird(JobParams params) {
        Set<String> allOffSaleOneIds = new HashSet<>();
        int currentPage = 1;
        long totalRecords = 0;
        long totalPages = 0;
        boolean isFirstPage = true;
        while (true) {
            try {
                KnetGroupGetNotOnSaleProductsReq req = KnetGroupGetNotOnSaleProductsReq.createDefaultReq(currentPage, params.pageSize);
                log.info("正在获取第三方系统已下架商品数据，第 {} 页，每页 {} 条", currentPage, params.pageSize);
                XxlJobHelper.log("正在获取第三方系统已下架商品数据，第 {} 页，每页 {} 条", currentPage, params.pageSize);
                IPage<KnetNotOnSaleProductVo> pageResult = thirdApiService.getNotOnSaleProducts(req);
                if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
                    log.info("第 {} 页没有数据，结束分页获取", currentPage);
                    XxlJobHelper.log("第 {} 页没有数据，结束分页获取", currentPage);
                    break;
                }
                // 第一页时记录总数信息
                if (isFirstPage) {
                    totalRecords = pageResult.getTotal();
                    totalPages = pageResult.getPages();
                    log.info("第三方系统总共有 {} 条已下架记录，分为 {} 页", totalRecords, totalPages);
                    XxlJobHelper.log("第三方系统总共有 {} 条已下架记录，分为 {} 页", totalRecords, totalPages);
                    isFirstPage = false;
                    // 检查是否超过最大页数限制
                    if (totalPages > params.maxPages) {
                        log.warn("总页数 {} 超过最大处理页数限制 {}，将只处理前 {} 页", totalPages, params.maxPages, params.maxPages);
                        XxlJobHelper.log("总页数 {} 超过最大处理页数限制 {}，将只处理前 {} 页", totalPages, params.maxPages, params.maxPages);
                    }
                }
                List<KnetNotOnSaleProductVo> pageData = pageResult.getRecords();
                // 提取oneId并过滤空值
                Set<String> pageOneIds = pageData
                        .stream()
                        .map(KnetNotOnSaleProductVo::getOneId)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toSet());
                allOffSaleOneIds.addAll(pageOneIds);
                log.info("第 {} 页获取到 {} 条数据，有效oneId {} 个，累计 {} 个，进度: {}/{}",
                        currentPage, pageData.size(), pageOneIds.size(), allOffSaleOneIds.size(), currentPage, totalPages);
                XxlJobHelper.log("第 {} 页获取到 {} 条数据，有效oneId {} 个，累计 {} 个，进度: {}/{}",
                        currentPage, pageData.size(), pageOneIds.size(), allOffSaleOneIds.size(), currentPage, totalPages);
                // 检查是否已经是最后一页或达到最大页数限制
                if (currentPage >= totalPages || currentPage >= params.maxPages) {
                    if (currentPage >= params.maxPages) {
                        log.warn("已达到最大处理页数限制 {}，停止获取更多数据", params.maxPages);
                        XxlJobHelper.log("已达到最大处理页数限制 {}，停止获取更多数据", params.maxPages);
                    } else {
                        log.info("已处理完所有 {} 页数据", totalPages);
                        XxlJobHelper.log("已处理完所有 {} 页数据", totalPages);
                    }
                    break;
                }
                currentPage++;
                // 添加短暂延迟，避免对第三方系统造成过大压力
                Thread.sleep(params.requestDelay);
            } catch (Exception e) {
                log.error("获取第三方系统第 {} 页数据失败: {}", currentPage, e.getMessage(), e);
                XxlJobHelper.log("获取第三方系统第 {} 页数据失败: {}", currentPage, e.getMessage());
                // 如果连续失败次数过多，则终止任务
                if (currentPage > 1) {
                    log.error("获取数据失败，终止任务执行");
                    XxlJobHelper.log("获取数据失败，终止任务执行");
                    throw new ServiceException("获取第三方系统数据失败: " + e.getMessage());
                }
                break;
            }
        }
        return allOffSaleOneIds;
    }

    /**
     * 分批处理下架操作
     *
     * @param oneIdsToOffSale 需要下架的oneId集合
     * @param batchSize       批处理大小
     * @return 总更新数量
     */
    private int processBatchOffSale(Set<String> oneIdsToOffSale, int batchSize) {
        List<String> oneIdsList = new ArrayList<>(oneIdsToOffSale);
        int totalUpdated = 0;
        int totalBatches = (oneIdsList.size() + batchSize - 1) / batchSize;
        log.info("开始分批下架操作，总数量: {}，批大小: {}，总批次: {}", oneIdsList.size(), batchSize, totalBatches);
        XxlJobHelper.log("开始分批下架操作，总数量: {}，批大小: {}，总批次: {}", oneIdsList.size(), batchSize, totalBatches);
        for (int i = 0; i < oneIdsList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, oneIdsList.size());
            List<String> batchOneIds = oneIdsList.subList(i, endIndex);
            int currentBatch = (i / batchSize) + 1;
            try {
                log.info("执行第 {}/{} 批下架操作，本批数量: {}", currentBatch, totalBatches, batchOneIds.size());
                XxlJobHelper.log("执行第 {}/{} 批下架操作，本批数量: {}", currentBatch, totalBatches, batchOneIds.size());
                int batchUpdated = knetProductOperationService.updateExistingProductsToOffSale(batchOneIds);
                totalUpdated += batchUpdated;
                log.info("第 {}/{} 批下架完成，本批更新: {} 个，累计更新: {} 个",
                        currentBatch, totalBatches, batchUpdated, totalUpdated);
                XxlJobHelper.log("第 {}/{} 批下架完成，本批更新: {} 个，累计更新: {} 个",
                        currentBatch, totalBatches, batchUpdated, totalUpdated);
                // 批次间添加短暂延迟，避免数据库压力过大
                if (currentBatch < totalBatches) {
                    Thread.sleep(50);
                }
            } catch (Exception e) {
                log.error("第 {}/{} 批下架操作失败: {}", currentBatch, totalBatches, e.getMessage(), e);
                XxlJobHelper.log("第 {}/{} 批下架操作失败: {}", currentBatch, totalBatches, e.getMessage());
                // 继续处理下一批，不中断整个任务
            }
        }
        return totalUpdated;
    }

    /**
     * 任务参数配置类
     */
    private static class JobParams {
        /**
         * 每页数据量，针对100万+数据优化，设置为1000
         */
        int pageSize = 1000;

        /**
         * 最大处理页数，默认2000，防止无限循环
         */
        int maxPages = 2000;

        /**
         * 批处理大小，下架操作分批执行，避免单次操作数据量过大
         */
        int batchSize = 500;

        /**
         * 请求延迟（毫秒），避免对第三方系统造成压力
         */
        long requestDelay = 50L;
    }
}
